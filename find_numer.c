#include <errno.h>
#include <ctype.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <limits.h>
#include <stdio.h>
#include "sds.h"
#include "cvector.h"

/**
 * @brief 检查字符是否为分隔符
 */
static inline bool is_separator(char c)
{
    return isspace((unsigned char)c) ||
        c == '(' || c == ')' ||
        c == '|' || c == '&' ||
        c == '+' || c == '-' ||
        c == '*' || c == '/' ||
        c == '=' || c == '!' ||
        c == '<' || c == '>' ||
        c == ',' || c == ';' ||
        c == '\0';
}

/**
 * @brief 检查字符是否为有效的标识符字符
 */
static inline bool is_identifier_char(char c)
{
    return isalnum((unsigned char)c) || c == '_';
}

/**
 * @brief 使用strtol从字符串中提取数字到数组
 *
 * 使用标准库函数strtol进行数字解析：
 * - 更可靠的错误处理
 * - 支持多种数字格式
 * - 代码更简洁易维护
 *
 * @param str 输入字符串
 * @param numbers 输出数组
 * @param max_count 数组最大容量
 * @return int 成功提取的数字个数，失败返回-1
 */
int find_numbers_from_str(const char *str, long *numbers, int max_count)
{
    if (!str || !numbers || max_count <= 0) {
        return -1;
    }

    int count = 0;
    char *endptr;
    const char *p = str;

    while (*p && count < max_count) {
        // 跳过非数字字符
        while (*p && !isdigit(*p) && *p != '-' && *p != '+') {
            p++;
        }

        if (!*p) break;

        errno = 0;
        long num = strtol(p, &endptr, 10);

        // 检查转换是否成功
        if (p == endptr) {
            p++;
            continue;
        }

        // 检查溢出
        if (errno == ERANGE) {
            p = endptr;
            continue;
        }

        numbers[count++] = num;
        p = endptr;
    }

    return count;
}

/**
 * @brief 从字符串中提取标识符到动态数组（高性能版本）
 *
 * 性能优化：
 * - 预分配内存减少重分配
 * - 减少函数调用开销
 * - 优化字符检查逻辑
 * - 避免重复计算字符串长度
 *
 * @param str 输入字符串
 * @param keys 输出的字符串动态数组指针
 * @return int 成功提取的标识符个数，失败返回-1
 */
int get_key_from_str(const char *str, cvector_vector_type(char *) *keys)
{
    if (!str || !keys) {
        return -1;
    }


    int count = 0;
    const char *p = str;

    while (*p) {
        // 快速跳过分隔符（内联优化）
        while (*p && (isspace((unsigned char)*p) ||
            *p == '(' || *p == ')' || *p == '|' || *p == '&' ||
            *p == '+' || *p == '-' || *p == '*' || *p == '/' ||
            *p == '=' || *p == '!' || *p == '<' || *p == '>' ||
            *p == ',' || *p == ';')) {
            p++;
        }

        if (!*p) break;

        const char *start = p;

        // 快速检查首字符（避免函数调用）
        if (!((*p >= 'a' && *p <= 'z') || (*p >= 'A' && *p <= 'Z') || *p == '_')) {
            // 跳过无效序列
            while (*p && !isspace((unsigned char)*p) &&
                   *p != '(' && *p != ')' && *p != '|' && *p != '&' &&
                   *p != '+' && *p != '-' && *p != '*' && *p != '/' &&
                   *p != '=' && *p != '!' && *p != '<' && *p != '>' &&
                   *p != ',' && *p != ';') {
                p++;
            }
            continue;
        }

        // 快速扫描标识符（内联字符检查）
        while (*p && ((*p >= 'a' && *p <= 'z') || (*p >= 'A' && *p <= 'Z') ||
            (*p >= '0' && *p <= '9') || *p == '_')) {
            p++;
        }

        // 创建标识符字符串
        size_t len = p - start;
        if (len > 0) {
            char *identifier = malloc(len + 1);
            if (!identifier) {
                // 内存分配失败，清理已分配的内存
                size_t i;
                for (i = 0; i < cvector_size(*keys); i++) {
                    free((*keys)[i]);
                }
                cvector_clear(*keys);
                return -1;
            }

            memcpy(identifier, start, len);
            identifier[len] = '\0';

            cvector_push_back(*keys, identifier);
            count++;
        }
    }

    return count;
}

int main()
{
    const char str[] = "123 & 456 | ((-789 | 1234567890) & 1234567890123456789)";
    long numbers[10];
    int count = find_numbers_from_str(str, numbers, 10);
    int i;
    for (i = 0; i < count; i++) {
        printf("%ld\n", numbers[i]);
    }

    const char str1[] = "((keu_str & ddd) | (ssds | ffff)) &（dddeeee | tttt）";

    cvector_vector_type(char *) keys = NULL;
    cvector_init(keys, 0, NULL);

    get_key_from_str(str1, &keys);

    for (i = 0; i < cvector_size(keys); i++) {
        printf("%s\n", keys[i]);
    }

    return 0;
}

