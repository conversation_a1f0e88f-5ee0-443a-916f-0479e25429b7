

#include <arpa/inet.h>
#include <sys/socket.h>
#include <netdb.h>
#include <ifaddrs.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>

int get_local_ip(char *ip, size_t ip_len)
{
    struct ifaddrs *ifaddr, *ifa;
    int family, s;

    if (getifaddrs(&ifaddr) == -1) {
        return -1;
    }

    // 遍历网络接口列表
    for (ifa = ifaddr; ifa != NULL; ifa = ifa->ifa_next) {
        if (ifa->ifa_addr == NULL) {
            continue;
        }

        family = ifa->ifa_addr->sa_family;

        // 只获取IPv4的地址
        if (family == AF_INET) {
            s = getnameinfo(ifa->ifa_addr,
                    sizeof(struct sockaddr_in),
                    ip, ip_len,
                    NULL, 0, NI_NUMERICHOST);
            if (s != 0) {
                freeifaddrs(ifaddr);
                return -1;
            }

            // 跳过本地回环地址
            if (strcmp(ip, "127.0.0.1") == 0) {
                continue;
            }

            // 找到第一个非回环地址即返回
            freeifaddrs(ifaddr);
            return 0;
        }
    }

    freeifaddrs(ifaddr);
    return -1;
}

int main()
{
    char ip[16] = {0}; // IPv4地址的最大长度为15个字符加上'\0'

    if (get_local_ip(ip, sizeof(ip)) == 0) {
        printf("本机IP地址: %s\n", ip);
    } else {
        printf("获取IP地址失败\n");
    }

    const char *paths[] = {"/http/url", "/fileinfo/filename", "/http/http_content_type"};

    size_t count = sizeof(paths) / sizeof(paths[0]);
    printf("count: %zu\n", count);

    int i;
    for (i = 0; i < sizeof(paths) / sizeof(paths[0]); i++) {
        printf("path: %s\n", paths[i]);
    }

    return 0;
}
