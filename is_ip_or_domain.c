#include <stdio.h>
#include <string.h>
#include <ctype.h>
#include <arpa/inet.h>

int is_ip_or_domain(const char *input) {
    struct sockaddr_in sa;
    struct sockaddr_in6 sa6;

    // 尝试检测IPv4
    if (inet_pton(AF_INET, input, &(sa.sin_addr)) == 1) {
        return 1;  // 输入是一个有效的IPv4地址
    }

    // 尝试检测IPv6
    if (inet_pton(AF_INET6, input, &(sa6.sin6_addr)) == 1) {
        return 1;  // 输入是一个有效的IPv6地址
    }

    // 检查是否是域名
    // 简单的检查域名是否包含有效字符
    const char *ptr = input;
    while (*ptr) {
        if (!(isalnum(*ptr) || *ptr == '-' || *ptr == '.')) {
            return -1; // 输入无效
        }
        ptr++;
    }

    // 域名可以包含字母、数字、短横线和点
    return 0; // 输入是一个域名
}

int main() {
    char input[256];

    printf("请输入一个IP地址或域名: ");
    scanf("%255s", input);

    int result = is_ip_or_domain(input);
    if (result == 1) {
        printf("输入是一个有效的IP地址。\n");
    } else if (result == 0) {
        printf("输入是一个有效的域名。\n");
    } else {
        printf("输入无效。\n");
    }

    return 0;
}