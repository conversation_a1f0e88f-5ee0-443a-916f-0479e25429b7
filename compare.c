
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>


/**
 * @brief 构建KMP算法的next数组
 * @param pattern 模式串
 * @param next 用于存储next数组的指针
 */
static void get_next(const char *pattern, int *next)
{
    int patternLength = strlen(pattern);
    next[0] = -1;
    int i = 0, j = -1;

    while (i < patternLength - 1) {
        if (j == -1 || pattern[i] == pattern[j]) {
            i++;
            j++;
            // 优化：当前缀后缀相同时，跳过
            if (pattern[i] != pattern[j]) {
                next[i] = j;
            } else {
                next[i] = next[j];
            }
        } else {
            j = next[j];
        }
    }
}

/**
 * @brief 使用KMP算法进行字符串匹配
 * @param text 主文本串
 * @param pattern 模式串
 * @return 返回模式串在主文本串中首次出现的位置，如果未找到则返回-1, 如果字符串相等，则返回0
 */
int util_kmp_match(const char *text, const char *pattern)
{
    if (!text || !pattern) {
        return -1;  // 输入无效
    }

    int textLength = strlen(text);
    int patternLength = strlen(pattern);

    if (patternLength == 0) {
        return 0;  // 空模式串总是匹配成功
    }
    if (textLength < patternLength) {
        return -1;  // 主文本串长度小于模式串，无法匹配
    }

    int *next = (int *)malloc(sizeof(int) * patternLength);
    if (!next) {
        return -1;  // 内存分配失败
    }
    get_next(pattern, next);

    int i = 0, j = 0;
    while (i < textLength && j < patternLength) {
        if (j == -1 || text[i] == pattern[j]) {
            i++;
            j++;
        } else {
            j = next[j];
        }
    }

    free(next);

    if (j == patternLength) {
        return i - j;  // 找到匹配，返回起始位置
    }
    return -1;  // 未找到匹配
}


int main(int argc, char *argv[])
{
    char *text = "World!";
    char *pattern = "world";
    int pos = util_kmp_match(text, pattern);
    printf("Text: %s\n", text);
    printf("Pattern: %s\n", pattern);
    printf("Position: %d\n", pos);
    if (pos >= 0) {
        printf("Found at position %d\n", pos);
    } else {
        printf("Not found\n");
    }


    
    return 0;
}
