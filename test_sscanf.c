#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include <errno.h>
#include <limits.h>
#include <float.h>
#include <math.h>
#include <complex.h>
#include <stdbool.h>
#include <stdint.h>
#include <inttypes.h>
#include <wchar.h>
#include <wctype.h>
#include <locale.h>
#include <time.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/time.h>


int main(int argc, char *argv[])
{
    const char *str = "2001\t0\t172.16.6.4-**********;***********-***********";
    int a, b;
    char ip1[256];
    char *ip2 = NULL;

    // sscanf() is used to read formatted input from a string
    if (sscanf(str, "%d\t%d\t%s", &a, &b, ip2) != 3) {
        fprintf(stderr, "sscanf failed\n");
        return 1;
    }
    printf("a: %d, b: %d, str: %s\n", a, b, ip2);
    return 0;
}