#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <unistd.h>
#include <time.h> // for time() and difftime()
#include <sys/sysinfo.h>
#include <ctype.h> // for isdigit()
#define _GNU_SOURCE // for strdup()

// 定义内存使用率阈值
#define MEMORY_THRESHOLD_PERCENT 80.0

// 定义 I/O 繁忙度阈值 (例如，如果 I/O 设备持续忙碌超过 70% 的时间，则触发)
// 注意：这个值需要根据实际的系统负载和需求来调整
#define IO_BUSY_THRESHOLD_PERCENT 70.0

// 定义采样间隔时间（秒），用于 I/O 检查
#define IO_SAMPLING_INTERVAL_SEC 2

// 结构体用于存储内存信息
typedef struct {
    long mem_total_kb;
    long mem_available_kb;
} MemoryInfo;

// 结构体用于存储 I/O 统计信息（我们主要关注读写时间）
// 注意：/proc/diskstats 格式复杂，这里只提取了我们关心的部分
typedef struct {
    unsigned long long read_ios;       // 成功完成的读请求数
    unsigned long long read_sectors;   // 读扇区数
    unsigned long long write_ios;      // 成功完成的写请求数
    unsigned long long write_sectors;  // 写扇区数
    unsigned long long read_time_ms;   // 读操作花费的总毫秒数
    unsigned long long write_time_ms;  // 写操作花费的总毫秒数
    unsigned long long io_time_ms;     // I/O 操作花费的总毫秒数
} DiskStats;

/**
 * @brief 自我保护机制 - 检查系统内存使用情况（简化版本）
 *
 * 当系统内存使用率超过80%时返回true，触发保护机制
 *
 * @return true 需要启动保护机制（内存使用率>80%）
 * @return false 系统状态正常
 */
static bool auto_protect_mech_simple(void)
{
    struct sysinfo info;

    // 获取系统信息失败时，为安全起见返回false继续处理
    if (sysinfo(&info) != 0) {
        return false;
    }

    printf("Uptime: %ld seconds\n", info.uptime);
    printf("Load Average (1/5/15 min): %.2f / %.2f / %.2f\n",
           (double)info.loads[0] / 65536.0, // Load averages are scaled by 2^16
           (double)info.loads[1] / 65536.0,
           (double)info.loads[2] / 65536.0);
    printf("Total RAM: %lu bytes (%.2f MB)\n", info.totalram, (double)info.totalram / (1024.0 * 1024.0));
    printf("Free RAM: %lu bytes (%.2f MB)\n", info.freeram, (double)info.freeram / (1024.0 * 1024.0));
    printf("Shared RAM: %lu bytes\n", info.sharedram);
    printf("Buffer RAM: %lu bytes\n", info.bufferram);
    printf("Free Swap: %lu bytes\n", info.freeswap);
    printf("Total Swap: %lu bytes\n", info.totalswap);
    printf("Number of Processes: %hu\n", info.procs);

        // 计算内存使用率
    unsigned long total_mem = info.totalram * info.mem_unit;
    unsigned long free_mem = info.freeram * info.mem_unit;
    unsigned long used_mem = total_mem - free_mem;

    // 计算使用率百分比
    double usage_percent = (double)used_mem / total_mem * 100.0;

    // 内存使用率超过80%时启动保护机制
    return usage_percent > 80.0;
}

// -------------------- 内存检查函数 --------------------
bool check_memory(MemoryInfo *mem_info)
{
    FILE *fp = NULL;
    char line[256];
    bool found_total = false, found_available = false;

    fp = fopen("/proc/meminfo", "r");
    if (fp == NULL) {
        perror("Error opening /proc/meminfo");
        return false; // 无法检查，返回 false
    }

    while (fgets(line, sizeof(line), fp) != NULL) {
        if (strncmp(line, "MemTotal:", 9) == 0) {
            // 格式: MemTotal:       16303680 kB
            sscanf(line, "MemTotal: %ld kB", &mem_info->mem_total_kb);
            found_total = true;
        } else if (strncmp(line, "MemAvailable:", 13) == 0) {
            // 格式: MemAvailable:   12345678 kB
            sscanf(line, "MemAvailable: %ld kB", &mem_info->mem_available_kb);
            found_available = true;
        }
        // 如果都找到了，可以提前退出，提高效率
        if (found_total && found_available) {
            break;
        }
    }

    fclose(fp);

    if (!found_total || !found_available) {
        fprintf(stderr, "Error: Could not find MemTotal or MemAvailable in /proc/meminfo\n");
        return false; // 信息不完整，返回 false
    }

    // 防止除以零
    if (mem_info->mem_total_kb == 0) {
        return false;
    }

    // 计算内存使用率 (使用可用内存来计算，这更准确)
    // 使用率 = (总内存 - 可用内存) / 总内存 * 100
    double used_memory_kb = (double)mem_info->mem_total_kb - mem_info->mem_available_kb;
    double memory_usage_percent = (used_memory_kb / mem_info->mem_total_kb) * 100.0;

    printf("Memory Usage: %.2f%%\n", memory_usage_percent);

    if (memory_usage_percent > MEMORY_THRESHOLD_PERCENT) {
        printf("Memory usage exceeded threshold (%.2f%% > %.2f%%)\n", memory_usage_percent, MEMORY_THRESHOLD_PERCENT);
        return true; // 内存使用率过高
    }

    return false; // 内存使用正常
}

// -------------------- I/O 检查函数 --------------------

// 辅助函数：解析 /proc/diskstats 文件，提取特定设备的 I/O 统计信息
// 返回值：true 如果成功，false 如果失败
bool get_disk_stats_for_device(const char *device_name, DiskStats *stats)
{
    FILE *fp = NULL;
    char line[512];
    bool found_device = false;

    fp = fopen("/proc/diskstats", "r");
    if (fp == NULL) {
        perror("Error opening /proc/diskstats");
        return false;
    }

    while (fgets(line, sizeof(line), fp) != NULL) {
        // /proc/diskstats 格式:
        //  8       0 hda: 100000 1000 20000 100 0 50000 3000 200000 0 10000 500
        //  major   minor   I/O errs    read sectors    writes sectors   read ms    write ms    io ms    ríos   w ríos   io Ríos
        //  field 1-3: 8       0 hda
        //  field 4: # reads completed successfully
        //  field 5: # reads merged
        //  field 6: # sectors read
        //  field 7: # milliseconds spent reading
        //  field 8: # writes completed successfully
        //  field 9: # writes merged
        //  field 10: # sectors written
        //  field 11: # milliseconds spent writing
        //  field 12: # I/O (in progress)
        //  field 13: # milliseconds spent doing I/Os
        //  field 14: # weighted I/O

        // 寻找匹配的设备名 (例如 "sda", "nvme0n1")
        // 这里为了简化，我们匹配设备名的开头部分
        if (strstr(line, device_name) != NULL && (line[0] == ' ' || line[0] == '\t')) {
            // 确保匹配的是整个设备名，而不是部分匹配
            // 例如，如果 device_name 是 "sda", 不应该匹配 "sda1"
            // 一个简单的检查是看设备名后面是否跟着一个数字（分区）
            // 更精确的匹配可以考虑使用 sscanf 和 %d 来解析设备ID，然后与设备名关联
            // 或者更简单地，我们只匹配设备名的起始位置
            char current_device_name[20]; // 足够容纳设备名
            unsigned int major, minor;
            unsigned long long read_ios_raw, read_sectors_raw, write_ios_raw, write_sectors_raw;
            unsigned long long read_time_ms_raw, write_time_ms_raw, io_time_ms_raw;

            // 尝试解析，跳过前三个字段（major, minor, device name）
            int fields_parsed = sscanf(line, "%u %u %*s %llu %*llu %llu %llu %*llu %llu %llu %llu %*llu %llu",
                                      &major, &minor,
                                      &read_ios_raw, &read_sectors_raw,
                                      &read_time_ms_raw,
                                      &write_ios_raw, &write_sectors_raw,
                                      &write_time_ms_raw, &io_time_ms_raw);

            // 提取设备名以进行更精确的比较
            // 找到设备名开始的位置
            char *device_name_ptr = strstr(line, " ") + 1; // 假设第一个空格后是设备名
            while (*device_name_ptr == ' ') device_name_ptr++; // 跳过开头的空格
            sscanf(device_name_ptr, "%19s", current_device_name); // 读取设备名

            // 检查是否是我们要找的设备，并且后面没有分区号（数字）
            bool is_target_device = true;
            if (strcmp(current_device_name, device_name) != 0) {
                 // 如果是设备名开头匹配，但不是完全匹配，例如 "sda" 匹配到 "sda1"
                 // 检查设备名后面是否跟着数字，如果是，则跳过
                for (int i = strlen(device_name); current_device_name[i] != '\0'; ++i) {
                    if (isdigit(current_device_name[i])) {
                        is_target_device = false;
                        break;
                    }
                }
                if (!is_target_device) {
                    continue; // 跳过这个设备
                }
            }


            if (fields_parsed == 11) { // 确认解析了11个我们关心的数值字段
                // 复制到输出结构体
                stats->read_ios = read_ios_raw;
                stats->read_sectors = read_sectors_raw;
                stats->write_ios = write_ios_raw;
                stats->write_sectors = write_sectors_raw;
                stats->read_time_ms = read_time_ms_raw;
                stats->write_time_ms = write_time_ms_raw;
                stats->io_time_ms = io_time_ms_raw;
                found_device = true;
                break; // 找到目标设备，退出循环
            } else {
                 // fprintf(stderr, "Warning: Could not parse diskstats line for device starting with '%s'. Parsed fields: %d\n", device_name, fields_parsed);
                 // 尝试匹配下一个设备
            }
        }
    }

    fclose(fp);
    return found_device;
}

// 辅助函数：计算 I/O 繁忙度
// 接收两次采样的数据，并根据采样间隔计算繁忙度
double calculate_io_utilization(const DiskStats *old_stats, const DiskStats *new_stats, unsigned int interval_sec)
{
    if (interval_sec == 0) return 0.0; // 防止除以零

    // I/O 繁忙度 = (两次采样期间 I/O 操作花费的总毫秒数) / (两次采样的时间间隔总毫秒数) * 100
    unsigned long long time_spent_io_diff = new_stats->io_time_ms - old_stats->io_time_ms;
    unsigned long long total_time_interval_ms = (unsigned long long)interval_sec * 1000;

    // 避免负数（如果时钟回退，尽管概率很小）
    if (new_stats->io_time_ms < old_stats->io_time_ms) {
        time_spent_io_diff = 0; // 或者可以考虑更复杂的处理，但对于简单场景，设为0即可
    }

    // 防止除以零
    if (total_time_interval_ms == 0) return 0.0;

    // I/O 繁忙度是一个百分比
    double io_utilization = ((double)time_spent_io_diff / (double)total_time_interval_ms) * 100.0;

    // 确保百分比在 0-100 之间
    if (io_utilization < 0.0) io_utilization = 0.0;
    if (io_utilization > 100.0) io_utilization = 100.0; // 理论上不会超过100%

    return io_utilization;
}

// 核心 I/O 检查函数
// 注意：这个函数需要两次调用来计算繁忙度，所以 auto_protect_mech 需要处理这个状态
bool check_io_high_load(double *current_utilization)
{
    static DiskStats previous_stats; // 静态变量，保存上次的统计数据
    static time_t last_check_time = 0; // 静态变量，保存上次检查的时间
    static bool first_run = true;      // 标志，用于首次运行

    time_t current_time;
    DiskStats current_stats;
    bool io_high = false;

    // 查找系统上的主硬盘设备，通常是 "sda", "nvme0n1" 等
    // 实际系统中可能有多个硬盘，这里为了简化，我们只检查一个（例如第一个找到的）
    // 更健壮的实现会迭代所有块设备
    const char *primary_device = NULL;

    // 尝试查找常见的设备名
    if (access("/sys/block/sda", F_OK) == 0) {
        primary_device = "sda";
    } else if (access("/sys/block/nvme0n1", F_OK) == 0) {
        primary_device = "nvme0n1";
    } else {
        // 如果找不到 sda 或 nvme0n1，尝试找其他设备，或者抛出错误
        // 这里为了演示，我们假设存在某个设备
        // 实际情况需要更智能地查找
        fprintf(stderr, "Warning: Could not find common disk devices like 'sda' or 'nvme0n1'. Attempting to check the first available device from /proc/diskstats.\n");
        // 尝试解析 /proc/diskstats 中的第一个设备
        FILE *fp_disk = fopen("/proc/diskstats", "r");
        if (fp_disk) {
            char line[512];
            if (fgets(line, sizeof(line), fp_disk)) {
                char dev_name_buffer[20];
                sscanf(line, "%*u %*u %19s", dev_name_buffer); // 解析设备名
                primary_device = strdup(dev_name_buffer); // 动态分配内存，后续需要free
            }
            fclose(fp_disk);
        }
        if (!primary_device) {
            fprintf(stderr, "Error: No disk devices found or could not parse /proc/diskstats.\n");
            return false;
        }
    }

    // 第一次运行，只需要记录当前的统计数据和时间
    if (first_run) {
        if (get_disk_stats_for_device(primary_device, &current_stats)) {
            previous_stats = current_stats;
            last_check_time = time(NULL);
            first_run = false;
            printf("IO Check: Initialized with device '%s'. Waiting for sampling interval...\n", primary_device);
            if (primary_device && primary_device != "sda" && primary_device != "nvme0n1") { // 如果是动态分配的
                free((void *)primary_device); // 释放内存
            }
            return false; // 第一次运行，不触发保护
        } else {
            fprintf(stderr, "Error: Could not get initial disk stats for device '%s'.\n", primary_device);
            if (primary_device && primary_device != "sda" && primary_device != "nvme0n1") {
                free((void *)primary_device);
            }
            return false;
        }
    }

    // 检查是否已达到采样间隔
    current_time = time(NULL);
    if ((current_time - last_check_time) < IO_SAMPLING_INTERVAL_SEC) {
        // printf("IO Check: Not enough time has passed since the last check.\n");
        return false; // 还没有到下一个采样时间
    }

    // 进行第二次采样
    if (get_disk_stats_for_device(primary_device, &current_stats)) {
        double utilization = calculate_io_utilization(&previous_stats, &current_stats, IO_SAMPLING_INTERVAL_SEC);
        *current_utilization = utilization; // 返回当前的 I/O 繁忙度

        printf("IO Utilization for device '%s': %.2f%%\n", primary_device, utilization);

        if (utilization > IO_BUSY_THRESHOLD_PERCENT) {
            printf("IO utilization exceeded threshold (%.2f%% > %.2f%%)\n", utilization, IO_BUSY_THRESHOLD_PERCENT);
            io_high = true;
        }

        // 更新上次的统计数据和时间，为下一次采样做准备
        previous_stats = current_stats;
        last_check_time = current_time;

        if (primary_device && primary_device != "sda" && primary_device != "nvme0n1") {
            free((void *)primary_device);
        }
        return io_high; // 返回是否 I/O 过载
    } else {
        fprintf(stderr, "Error: Could not get disk stats for device '%s' during second sample.\n", primary_device);
        if (primary_device && primary_device != "sda" && primary_device != "nvme0n1") {
            free((void *)primary_device);
        }
        return false;
    }
}

// -------------------- 主保护机制函数 --------------------

/**
 * @brief 检查系统情况（内存和 I/O），当内存使用率超过阈值或 I/O 繁忙度过高时返回 true。
 *
 * @param memory_usage_ptr 指向存储当前内存使用率的 double 变量的指针，可以为 NULL。
 * @param io_utilization_ptr 指向存储当前 I/O 繁忙度的 double 变量的指针，可以为 NULL。
 * @return bool 如果系统需要保护，返回 true；否则返回 false。
 */
bool auto_protect_mech(double *memory_usage_ptr, double *io_utilization_ptr)
{
    MemoryInfo mem_info;
    double current_io_utilization = 0.0;
    bool needs_protection = false;

    // 1. 检查内存
    if (check_memory(&mem_info)) {
        if (memory_usage_ptr) {
            // 计算并存储内存使用率
            if (mem_info.mem_total_kb > 0) {
                double used_memory_kb = (double)mem_info.mem_total_kb - mem_info.mem_available_kb;
                *memory_usage_ptr = (used_memory_kb / mem_info.mem_total_kb) * 100.0;
            } else {
                *memory_usage_ptr = 0.0;
            }
        }
        printf("System is under memory pressure. Triggering protection.\n");
        needs_protection = true;
    } else {
        if (memory_usage_ptr) {
            if (mem_info.mem_total_kb > 0) {
                double used_memory_kb = (double)mem_info.mem_total_kb - mem_info.mem_available_kb;
                *memory_usage_ptr = (used_memory_kb / mem_info.mem_total_kb) * 100.0;
            } else {
                *memory_usage_ptr = 0.0;
            }
        }
    }

    // 2. 检查 I/O (需要多次调用来累积 I/O 繁忙度)
    // check_io_high_load 会在每次被调用时，根据 IO_SAMPLING_INTERVAL_SEC
    // 来决定是否进行一次实际的 I/O 负载计算。
    // 因此，auto_protect_mech 只需要调用它，它内部会管理状态。
    if (check_io_high_load(&current_io_utilization)) {
        if (io_utilization_ptr) {
            *io_utilization_ptr = current_io_utilization;
        }
        printf("System is experiencing high I/O load. Triggering protection.\n");
        needs_protection = true;
    } else {
        if (io_utilization_ptr) {
            *io_utilization_ptr = current_io_utilization;
        }
    }

    return needs_protection;
}

// -------------------- 主函数，用于测试 --------------------
int main()
{
    printf("Starting system protection check...\n");

    double mem_usage = 0.0;
    double io_util = 0.0;

    // 模拟多次调用，以确保 I/O 检查能正确进行采样
    for (int i = 0; i < 5; ++i) {
        printf("\n--- Check Iteration %d ---\n", i + 1);
        if (auto_protect_mech(&mem_usage, &io_util)) {
            printf("Auto Protect Mechanism Activated!\n");
            printf("Current Memory Usage: %.2f%%\n", mem_usage);
            printf("Current IO Utilization: %.2f%%\n", io_util);
            // 在这里可以执行保护操作，例如：
            // - 限制进程 CPU 使用率
            // - 终止高内存/IO 消耗的进程
            // - 发送告警通知
            // ...
        } else {
            printf("System is operating within normal limits.\n");
            printf("Current Memory Usage: %.2f%%\n", mem_usage);
            printf("Current IO Utilization: %.2f%%\n", io_util);
        }

        // 等待一段时间，让系统有一些变化，并避免过于频繁的检查
        // 如果 IO_SAMPLING_INTERVAL_SEC 是 2 秒，那么这里可以稍微短一点，
        // 这样多次循环也能覆盖到 I/O 采样
        sleep(1); // 每秒进行一次检查，I/O 检查内部会处理采样间隔
    }

    printf("\nSystem protection check finished.\n");
    return 0;
}
