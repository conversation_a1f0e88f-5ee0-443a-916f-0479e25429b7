#define _GNU_SOURCE // for strdup() - 必须在所有头文件之前定义
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <unistd.h>
#include <time.h> // for time() and difftime()
#include <sys/sysinfo.h>
#include <ctype.h> // for isdigit()
#include <sys/statfs.h> // For statfs()

// 定义内存使用率阈值
#define MEMORY_THRESHOLD_PERCENT 80.0

/**
 * @brief 检查整个系统的 RAM（物理内存）使用情况
 * @return true 表示内存使用率过高，false 表示内存使用率正常
 */
static bool check_memory_usage()
{
    FILE *fp = NULL;
    char line[512] = {0};

    long mem_total_kb; // 总内存大小
    long mem_available_kb; // 可用内存大小

    bool found_total = false, found_available = false;

    fp = fopen("/proc/meminfo", "r");
    if (fp == NULL) {
        perror("Error opening /proc/meminfo");
        return false; // 无法检查，返回 false
    }

    while (fgets(line, sizeof(line), fp) != NULL) {
        if (strncmp(line, "MemTotal:", 9) == 0) {
            // 格式: MemTotal:       16303680 kB
            sscanf(line, "MemTotal: %ld kB", &mem_total_kb);
            found_total = true;
        } else if (strncmp(line, "MemAvailable:", 13) == 0) {
            // 格式: MemAvailable:   12345678 kB
            sscanf(line, "MemAvailable: %ld kB", &mem_available_kb);
            found_available = true;
        }
        // 如果都找到了，可以提前退出，提高效率
        if (found_total && found_available) {
            break;
        }
    }
    fclose(fp);

    if (!found_total || !found_available) {
        return false; // 信息不完整，返回 false
    }

    // 防止除以零
    if (mem_total_kb == 0) {
        return false;
    }

    // 计算内存使用率 (使用可用内存来计算，这更准确)
    // 使用率 = (总内存 - 可用内存) / 总内存 * 100
    double used_memory_kb = (double)mem_total_kb - mem_available_kb;
    double memory_usage_percent = (used_memory_kb / mem_total_kb) * 100.0;


    if (memory_usage_percent > MEMORY_THRESHOLD_PERCENT) {
        return true; // 内存使用率过高
    }

    return false; // 内存使用正常
}

/**
 * @brief 检查 /dev/shm 目录的使用率是否超过阈值。
 *
 * @return bool 如果 /dev/shm 使用率超过阈值，返回 true；否则返回 false。
 */
static bool check_dev_shm_usage()
{
    struct statfs fs_stat;
    const char *path = "/dev/shm";
    double usage_percentage = 0.0;

    // 获取文件系统信息
    if (statfs(path, &fs_stat) == -1) {
        return false; // 无法检查，返回 false
    }

    // f_blocks: 文件系统的总块数
    // f_bavail: 可用块数
    // f_bsize: 块大小
    // 注意：f_blocks 和 f_bavail 是以 f_bsize 为单位的。
    // 为了更精确，我们可以直接使用 fs_stat.f_blocks * fs_stat.f_bsize
    // 和 (fs_stat.f_blocks - fs_stat.f_bavail) * fs_stat.f_bsize
    // 或者直接使用块的数量进行计算，因为块大小是相同的。

    // 总空间 (以字节为单位)
    long long total_space_bytes = (long long)fs_stat.f_blocks * fs_stat.f_bsize;

    // 可用空间 (以字节为单位)
    long long available_space_bytes = (long long)fs_stat.f_bavail * fs_stat.f_bsize;

    // 已使用空间 (以字节为单位)
    long long used_space_bytes = total_space_bytes - available_space_bytes;

    // 计算使用率
    if (total_space_bytes > 0) {
        usage_percentage = ((double)used_space_bytes / total_space_bytes) * 100.0;
    } else {
        usage_percentage = 0.0; // 避免除以零
    }

    if (usage_percentage > MEMORY_THRESHOLD_PERCENT) {
        return true; // /dev/shm 使用过高
    }

    return false; // /dev/shm 使用正常
}

bool check_system_info()
{
    if (check_memory_usage()) {
        return true;
    }

    if (check_dev_shm_usage()) {
        return true;
    }

    return false;
}

// -------------------- 主函数，用于测试 --------------------
int main()
{
    printf("Starting system protection check...\n");

    for (int i = 0; i < 5000; ++i) {
        if (check_system_info()) {
            printf("System is under memory pressure. Triggering protection.\n");
        } else {
            printf("System is operating within normal limits.\n");
        }

        sleep(1); // 每秒进行一次检查
    }

    printf("\nSystem protection check finished.\n");
    return 0;
}
