#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <sys/time.h>
#include <time.h>


void flusher_get_local_time(char *timestamp, char *timeusec)
{
    char buffer[80] = {0};
    struct tm nowTime;
    struct timeval curTime;
    gettimeofday(&curTime, NULL);

    int micro = curTime.tv_usec;

    localtime_r(&curTime.tv_sec, &nowTime);//把得到的值存入临时分配的内存中，线程安全
    strftime(timestamp, 80, "%Y-%m-%dT%H:%M:%S%z", &nowTime);
    sprintf(timeusec, "%d%06d", curTime.tv_sec, micro);
    snprintf(timeusec, 80, "%d%06d", curTime.tv_sec, micro);
    return;
}

void get_current_time_string(char *datetime)
{
    time_t now = time(NULL);
    struct tm nowTime;
    localtime_r(&now, &nowTime); // 线程安全的本地时间获取

    // 格式化时间为 "YYYYMMDDHHMMSS"
    //strftime(datetime, 15, "%Y%m%d%H%M%S", &nowTime);
    strftime(datetime, 15, "%m%d%H%M", &nowTime);
}

static char *get_suffix_by_url_filename(const char *svalue)
{
    if (!svalue) return NULL;

    char *pos = strrchr(svalue, '.');
    if (!pos) return NULL;

    char *end = strchr(pos, '?');
    if (!end) end = strchr(pos, '\0'); // 找不到 '?' 就指向字符串尾

    if (end == pos + 1) return NULL; // 防止返回空后缀

    return strndup(pos + 1, end - pos - 1); // 返回新分配内存中的后缀
}

int main()
{
    const char *svalue = "http://172.16.4.97:8989/go-admin-ui/static/js/chunk-vendors.3cc69f39.js";
    char *buf = get_suffix_by_url_filename(svalue);
    printf("buf=%s\n", buf);
    free(buf);


    char timestamp[80] = {0};
    char timeusec[80] = {0};
    flusher_get_local_time(timestamp, timeusec);
    printf("timestamp=%s, timeusec=%s\n", timestamp);


    char datetime[15] = {0};
    get_current_time_string(datetime);
    printf("Current datetime: %s\n", datetime);

    return 0;
}
