

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

void check_underage(char *buff, int len)
{
    char ymd[10] = {0};   //yyyymmdd
    int birth_year;

    // 提取出生年份
    strncpy(ymd, buff + 6, 4);
    birth_year = atoi(ymd);

    // 获取当前时间
    time_t now = time(0);
    struct tm *tm_now = localtime(&now);
    int current_year = tm_now->tm_year + 1900;

    // 如果年份差大于18年，肯定成年
    if (current_year - birth_year > 18) {
        return;
    }
    // 如果年份差小于18年，肯定未成年
    else if (current_year - birth_year < 18) {
        printf("1未成年\n");
        return;
    }

    // 如果年份差正好18年，需要进一步比较月日
    strncpy(ymd, buff + 10, 4);  // 提取月日
    int birth_month = atoi(ymd) / 100;
    int birth_day = atoi(ymd) % 100;

    // 如果当前月份小于出生月份，或者月份相同但天数小于出生天数，则未满18周岁
    if (tm_now->tm_mon + 1 < birth_month ||
        (tm_now->tm_mon + 1 == birth_month && tm_now->tm_mday < birth_day)) {
        printf("2未成年\n");
    }

}
// void check_underage(char *buff, int len)
// {

//     char ymd[10];   //yyyymmdd
//     strncpy(ymd, buff + 6, 8);
//     time_t now = time(0);
//     struct tm ago = {0};
//     localtime_r(&now, &ago);
//     // 减去18年
//     ago.tm_year -= 18;
//     // 转换为time_t类型以便打印
//     time_t ago_ts = mktime(&ago);
//     //当前时间
//     time_t ts = cb_time_timestamp_to_time_t(ymd, "%Y%m%d");
//     int diff = (int)(ts - ago_ts);

//     if (diff > 0) {//小于18岁
//         printf("the age is %d\n", diff / 31536000);
//     } else {
//         printf("the age is %d\n", diff / 31536000);
//     }

// }

int main()
{
    char buff[1024] = "141127190107192792";
    check_underage(buff, strlen(buff));
    return 0;
}